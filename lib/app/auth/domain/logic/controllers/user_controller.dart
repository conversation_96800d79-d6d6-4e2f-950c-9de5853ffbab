import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/retailer.dart';
import 'package:td_commons_flutter/models/user.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/session_controller.dart';
import 'package:td_procurement/app/auth/domain/logic/listeners/on_before_login.dart';
import 'package:td_procurement/app/auth/domain/logic/listeners/on_before_logout.dart';
import 'package:td_procurement/app/auth/domain/logic/listeners/on_login.dart';
import 'package:td_procurement/app/auth/domain/logic/listeners/on_logout.dart';
import 'package:td_procurement/app/auth/domain/params/login_token.dart';
import 'package:td_procurement/app/auth/domain/use_cases/auth_use_cases.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_response.dart';
import 'package:td_procurement/core/services/intercom/intercom_manager.dart';
import 'package:td_procurement/core/services/shared_prefs/shared_prefs.dart';
import 'package:td_procurement/src/res/values/storage_keys/storage_keys.dart';
import 'package:td_procurement/src/utils/error_handler/error_handler.dart';

class UserController extends Notifier<User?> {
  @override
  User? build() {
    final savedUser = preferences.read(StorageKeys.user);

    User? user;
    if (savedUser != null) {
      try {
        user = User.fromMap(
          json.decode(savedUser),
        );
      } catch (error) {
        ErrorHandler.report(error, StackTrace.current);
      }
    }
    if (user != null) {
      getRetailOutlet(user);
    }
    return user;
  }

  getRetailOutlet(User user) async {
    final res = await ref.read(getRetailOutletUseCaseProvider(user));
    switch (res) {
      case Success():
        updateOutlet(res.data);
      case Failure _:
        onLogout();
    }
  }

  late SharedPrefs preferences = ref.read(storageProvider);

  Future<ApiResponse> loginWithOtp(LoginTokenParams params) async {
    final response = await ref.read(loginWithOtpUseCaseProvider(params));
    switch (response) {
      case Success<User>(data: var user):
        onLogin(user);
      case _:
        () {};
    }
    return response;
  }

  Future<void> onBeforeLogin() async {
    _sendUserOnBeforeLoginEvent();
  }

  Future<void> onBeforeLogout() async {
    _sendUserOnBeforeLogoutEvent();
  }

  Future<void> onLogin(User user) async {
    state = user;
    preferences.save(
      StorageKeys.user,
      json.encode(
        user.toMap(),
      ),
    );
    _sendUserLoginEvent(user);
  }

  updateOutlet(RetailOutlet outlet, [bool isLogin = true]) {
    User? currentUser = state;
    currentUser?.retailOutlets?.removeWhere((e) => e.id == outlet.id);
    currentUser = currentUser
        ?.copyWith(retailOutlets: [outlet, ...currentUser.retailOutlets!]);

    state = currentUser;
    preferences.save(
        StorageKeys.user,
        json.encode(
          currentUser?.toMap(),
        ));
    if (isLogin) {
      _sendUserLoginEvent(currentUser!);
    }
  }

  updateUser(User user) {
    state = user;
    preferences.save(
      StorageKeys.user,
      json.encode(
        user.toMap(),
      ),
    );
  }

  Future<void> onLogout() async {
    state = null;
    preferences.clear();
    _sendUserLogoutEvent();
  }

  Future<void> _sendUserLoginEvent(User user) async {
    for (var element in _stateListeners) {
      final stateObject = ref.read(element);
      if (stateObject is OnLogin) stateObject.onLogin(user);
    }
    await Future.value();
  }

  Future<void> _sendUserLogoutEvent() async {
    for (var element in _stateListeners) {
      final stateObject = ref.read(element);
      if (stateObject is OnLogout) stateObject.onLogout();
    }
    await Future.value();
  }

  Future<void> _sendUserOnBeforeLogoutEvent() async {
    for (var element in _stateListeners) {
      final stateObject = ref.read(element);
      if (stateObject is OnBeforeLogout) stateObject.onBeforeLogout();
    }
    await Future.value();
  }

  Future<void> _sendUserOnBeforeLoginEvent() async {
    for (var element in _stateListeners) {
      final stateObject = ref.read(element);
      if (stateObject is OnBeforeLogin) stateObject.onBeforeLogin();
    }
    await Future.value();
  }

  final List<Refreshable> _stateListeners = [
    sessionController,
    intercomManager,
  ];
}

final userControllerProvider = NotifierProvider<UserController, User?>(() {
  return UserController();
});
