import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/shipments/shipment_service.dart';
import 'package:td_procurement/app/shipments/shipment_state.dart';
import 'package:td_procurement/core/models/optional.dart';

import 'shipment_params.dart';

final shipmentControllerProvider =
    NotifierProvider<ShipmentController, ShipmentState>(
        () => ShipmentController());

class ShipmentController extends Notifier<ShipmentState> {
  late ShipmentService _shipmentService;

  @override
  ShipmentState build() {
    _shipmentService = ref.read(shipmentServiceProvider);

    // Watch user state to ensure controller rebuilds when user changes
    final user = ref.watch(userControllerProvider);

    // If user is null (logged out), return initial state
    if (user == null) {
      return ShipmentState.initial();
    }

    return ShipmentState.initial();
  }

  Future<void> fetchShipments(
    FetchShipmentsParam params, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    state = state.copyWith(
        fetchShipmentsParams: state.fetchShipmentsParams
            .copyWith(activeIndex: params.activeIndex));

    if (forced) {
      state = state.copyWith(shipments: const AsyncLoading());
    } else {
      if (params.currentPage == 1 &&
          (state.shipments.hasValue && state.shipments.value!.isNotEmpty)) {
        return;
      }

      // return if query has reached the max page or if loading or loading more orders
      if (state.fetchShipmentsParams.currentPage ==
              state.fetchShipmentsParams.totalPages ||
          state.shipments.isLoading ||
          state.fetchShipmentsParams.loadingMore) {
        return;
      }

      if (state.fetchShipmentsParams.loaded) {
        state = state.copyWith(
            fetchShipmentsParams:
                state.fetchShipmentsParams.copyWith(loadingMore: true));
      } else {
        state = state.copyWith(shipments: const AsyncLoading());
      }
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _shipmentService.fetchShipments(params);

      res.when(
        success: (data) {
          state = state.copyWith(
            shipments:
                AsyncData([...?state.shipments.value, ...data.shipments]),
            fetchShipmentsParams: params
                .fromQueryParams(data.queryParams)
                .copyWith(loaded: true, loadingMore: false),
          );
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            if (!state.fetchShipmentsParams.loaded) {
              state = state.copyWith(
                  shipments: AsyncError(e, StackTrace.current),
                  fetchShipmentsParams: state.fetchShipmentsParams
                      .copyWith(loaded: false, loadingMore: false));
            }
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> getShipment(
    String shippingId, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(shipment: const AsyncLoading());
    } else {
      if (state.shipment.valueOrNull?.id == shippingId) {
        return;
      } else {
        state = state.copyWith(shipment: const AsyncLoading());
      }
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _shipmentService.getShipment(shippingId);

      res.when(
        success: (data) {
          state = state.copyWith(shipment: AsyncData(data));
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(shipment: AsyncError(e, StackTrace.current));
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> fetchDocuments(
    String shippingId, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (state.shipment.valueOrNull?.id == shippingId) {
      return;
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _shipmentService.fetchDocuments(shippingId);

      res.when(
        success: (data) {
          state = state.copyWith(documents: data);
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  void setDateFilter(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(
        selectedStartDate: Optional(startDate),
        selectedEndDate: Optional(endDate));
  }
}
