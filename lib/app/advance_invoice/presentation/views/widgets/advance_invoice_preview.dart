import 'dart:async';
import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:td_commons_flutter/utils/methods.dart';
import 'package:td_procurement/app/advance_invoice/data/models/index.dart';
import 'package:td_procurement/app/advance_invoice/domain/params/line_item_param.dart';
import 'package:td_procurement/app/advance_invoice/domain/use_cases/advance_invoice_use_cases.dart';
import 'package:td_procurement/app/advance_invoice/presentation/logic/advance_invoice_controller.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/order/widgets/outlet_search_auto_complete.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/components/widgets/streamed_image.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class AdvanceInvoicePreviewWidget extends ConsumerWidget {
  const AdvanceInvoicePreviewWidget({super.key, required this.randomNumber});

  final String randomNumber;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final textTheme = context.textTheme;
    final settings = ref.watch(advanceInvoiceControllerProvider).settings;
    final outlet = ref.watch(userControllerProvider)?.currentRetailOutlet;
    final selectedCustomer = ref.watch(selectedCustomerProvider);
    final createParams = ref.watch(createAdvanceInvoiceProvider);
    final lineItems = createParams.lineItems;
    final taxes = createParams.taxes ?? [];
    final charges = createParams.charges ?? [];
    final billTo = selectedCustomer;
    final shipTo = selectedCustomer;
    final invoiceNumber = 'IVN-$randomNumber';
    final dueDate = DateTime.now().toDate();
    final subtotal =
        lineItems.fold<double>(0, (sum, item) => sum + (item.total));
    final subTotalWithTaxAndDiscount = lineItems.fold<double>(
        0, (sum, item) => sum + (item.totalWithTaxAndDiscount));
    final discount = createParams.discount;
    final note = createParams.note;
    final bankAccount = createParams.bankAccount;

    // Calculate taxes
    final taxAmount = taxes.fold<double>(0, (sum, tax) {
      final taxRate = tax.rate ?? 0;
      return sum + (subtotal * (taxRate / 100));
    });

    // Calculate charges
    final chargeAmount = charges.fold<double>(0, (sum, charge) {
      return sum + (charge.amount ?? 0);
    });

    // Calculate discount amount
    double discountAmount = 0;
    if (discount != null) {
      if (discount.type == DiscountType.fixed) {
        discountAmount = discount.value.toDouble();
      } else if (discount.type == DiscountType.percentage) {
        discountAmount = subtotal * (discount.value.toDouble() / 100);
      }
    }

    final total =
        subTotalWithTaxAndDiscount + taxAmount + chargeAmount - discountAmount;

    final currencyCode = ref.read(currencyCodeProvider);

    return Container(
      color: Palette.kFCFCFC,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        margin: const EdgeInsets.symmetric(horizontal: 40, vertical: 40),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8), topRight: Radius.circular(8)),
          boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 10)],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 40.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header row: Invoice, logo
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left: Invoice title and meta
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Invoice',
                            style: context.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                              fontSize: 18,
                              color: Palette.primaryBlack,
                            )),
                        const Gap(24),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Invoice number',
                                      style:
                                          context.textTheme.bodySmall?.copyWith(
                                        color: Colors.black54,
                                        fontWeight: FontWeight.w600,
                                        letterSpacing: 0.3,
                                      )),
                                  Text(invoiceNumber,
                                      style:
                                          context.textTheme.bodySmall?.copyWith(
                                        fontWeight: FontWeight.w600,
                                      )),
                                ],
                              ),
                            ),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Date due',
                                      style:
                                          context.textTheme.bodySmall?.copyWith(
                                        color: Colors.black54,
                                        fontWeight: FontWeight.w600,
                                        letterSpacing: 0.3,
                                      )),
                                  Text(dueDate,
                                      style:
                                          context.textTheme.bodySmall?.copyWith(
                                        fontWeight: FontWeight.w600,
                                      )),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Right: Logo
                  Container(
                    width: 64,
                    height: 64,
                    margin: const EdgeInsets.only(left: 24),
                    child: settings.when(
                      data: (data) {
                        if (data == null || (data.logoUrl?.isEmpty ?? true)) {
                          return InkWell(
                            onTap: () => showDialog(
                              context: context,
                              builder: (context) => const LogoUploadWidget(),
                            ),
                            child: Material(
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Palette.kE7E7E7.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(Icons.add_photo_alternate,
                                    size: 40, color: Palette.placeholder),
                              ),
                            ),
                          );
                        }
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child:
                              StreamedImageWidget(imageUrl: data.logoUrl ?? ''),
                        );
                      },
                      loading: () =>
                          const Center(child: CircularProgressIndicator()),
                      error: (error, stackTrace) => Container(
                        decoration: BoxDecoration(
                          color: Palette.kE7E7E7.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(Icons.add_photo_alternate,
                            size: 40, color: Palette.kE7E7E7),
                      ),
                    ),
                  ),
                ],
              ),
              const Gap(32),
              // Company, Bill to, Ship to
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Company info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(outlet?.outletBusinessName ?? '-',
                            style: context.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.2,
                            )),
                        Text(outlet?.formattedAddress ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(outlet?.country ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(outlet?.contactPhone ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(outlet?.email ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                      ],
                    ),
                  ),
                  Gap(5.w),
                  // Bill to
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Bill to',
                            style: context.textTheme.bodySmall?.copyWith(
                              color: Colors.black54,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.3,
                            )),
                        Text(billTo?.outletBusinessName ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.1,
                            )),
                        Text(billTo?.formattedAddress ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(billTo?.country ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(billTo?.contactPhone ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(billTo?.email ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                      ],
                    ),
                  ),
                  Gap(5.w),
                  // Ship to
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Ship to',
                            style: context.textTheme.bodySmall?.copyWith(
                              color: Colors.black54,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.3,
                            )),
                        Text(shipTo?.outletBusinessName ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.1,
                            )),
                        Text(shipTo?.formattedAddress ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(shipTo?.country ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(billTo?.phoneNumber ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                        Text(billTo?.email ?? '-',
                            style: context.textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            )),
                      ],
                    ),
                  ),
                ],
              ),
              const Gap(32),
              // Amount due and pay online
              Text.rich(
                TextSpan(
                    text: CurrencyWidget.value(context, currencyCode, total),
                    children: [
                      TextSpan(
                        text: ' due $dueDate',
                        style: context.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w800,
                          letterSpacing: 1.6,
                        ),
                      ),
                    ]),
                style: context.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w800,
                ),
              ),
              const Gap(32),
              // Items table
              _buildPreviewItemsTable(context, lineItems, currencyCode),
              const Gap(32),
              // Subtotal, shipping, total, amount due
              Row(
                children: [
                  Expanded(child: Container()),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        _buildSummaryRow(context, 'Subtotal',
                            subTotalWithTaxAndDiscount, currencyCode),
                        if (taxes.isNotEmpty) ...[
                          ...taxes.map((tax) => _buildSummaryRow(
                              context,
                              '${tax.name} (${tax.rate}%)',
                              (subtotal.toDouble() *
                                      ((tax.rate?.toDouble() ?? 0.0) / 100.0))
                                  .toDouble(),
                              currencyCode)),
                        ],
                        if (charges.isNotEmpty) ...[
                          ...charges.map((charge) => _buildSummaryRow(
                              context,
                              charge.name,
                              charge.amount?.toDouble() ?? 0,
                              currencyCode)),
                        ],
                        if (discountAmount > 0)
                          _buildSummaryRow(context, 'Discount', discountAmount,
                              currencyCode),
                        _buildSummaryRow(context, 'Total', total, currencyCode),
                        const Gap(8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('Amount Due',
                                style: context.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  letterSpacing: 0.2,
                                )),
                            const Gap(8),
                            Text(
                                CurrencyWidget.value(
                                    context, currencyCode, total),
                                style: context.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.w800,
                                  letterSpacing: 0.3,
                                )),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              // Note
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, -0.1),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    )),
                    child: FadeTransition(
                      opacity: animation,
                      child: child,
                    ),
                  );
                },
                child: note != null && note.isNotEmpty
                    ? Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          key: const ValueKey('note'),
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Gap(32),
                            Text(
                              note,
                              style: context.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.w500,
                                letterSpacing: 0.1,
                              ),
                              textAlign: TextAlign.left,
                            ),
                          ],
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, -0.1),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeInOut,
                    )),
                    child: FadeTransition(
                      opacity: animation,
                      child: child,
                    ),
                  );
                },
                child: bankAccount != null
                    ? Align(
                        alignment: Alignment.centerLeft,
                        child: Column(
                          key: const ValueKey('bank-details'),
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Gap(32),
                            Text(
                                'Account Name: ${bankAccount.accountName != null ? toTitleCase(bankAccount.accountName) : '-'}',
                                style: textTheme.bodySmall?.copyWith(
                                  letterSpacing: 0.1,
                                )),
                            Text(
                                'Account Number: ${bankAccount.accountNumber ?? '-'}',
                                style: textTheme.bodySmall?.copyWith(
                                  letterSpacing: 0.1,
                                )),
                            Text('Bank Name: ${bankAccount.bankName}',
                                style: textTheme.bodySmall?.copyWith(
                                  letterSpacing: 0.1,
                                )),
                          ],
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
              const Gap(32),
              // Footer summary
              Divider(
                color: Palette.kE7E7E7,
                height: 0,
                // indent: 0,
              ),
              const Gap(4),
              Text('$invoiceNumber · due $dueDate',
                  style: context.textTheme.bodySmall?.copyWith(
                    color: Colors.black54,
                    letterSpacing: 0.2,
                  )),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPreviewItemsTable(
      BuildContext context, List<LineItemParam> lineItems, String currency) {
    final textTheme = context.textTheme;
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: Table(
                columnWidths: const {
                  0: FlexColumnWidth(2.5), // Name
                  1: FlexColumnWidth(2), // Qty
                  2: FlexColumnWidth(2), // Unit price
                  3: FlexColumnWidth(2), // Tax rate
                  4: FlexColumnWidth(2), // Discount
                  5: FlexColumnWidth(2.5), // Amount
                },
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                children: [
                  TableRow(
                    decoration: const BoxDecoration(color: Color(0xFFF5F5F5)),
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text('Name',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.3,
                            ),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text('Qty',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.3,
                            ),
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text(
                            'Unit\nPrice (${CurrencyWidget.symbol(context, currency)})',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.3,
                            ),
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text('Tax',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.3,
                            ),
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text('Discount',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.3,
                            ),
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        child: Text(
                            'Total (${CurrencyWidget.symbol(context, currency)})',
                            style: textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w700,
                              letterSpacing: 0.3,
                            ),
                            textAlign: TextAlign.right),
                      ),
                    ],
                  ),
                  ...lineItems.map((item) {
                    return TableRow(children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 12),
                        child: Text(item.name,
                            style: textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            ),
                            textAlign: TextAlign.left),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 12),
                        child: Text(item.quantity.formattedAmount(context),
                            style: textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            ),
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 12),
                        child: Text(item.unitPrice.formattedAmount(context),
                            style: textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            ),
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 12),
                        child: Text('${item.taxRate}%',
                            style: textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            ),
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 12),
                        child: Text(
                            item.discount != null
                                ? item.discount!.type == DiscountType.fixed
                                    ? CurrencyWidget.value(
                                        context, currency, item.discount!.value)
                                    : '${item.discount!.value}%'
                                : '-',
                            style: textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            ),
                            textAlign: TextAlign.right),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 12),
                        child: Text(
                            item.totalWithTaxAndDiscount
                                .formattedAmount(context),
                            style: textTheme.bodySmall?.copyWith(
                              letterSpacing: 0.1,
                            ),
                            textAlign: TextAlign.right),
                      ),
                    ]);
                  }),
                ],
              ),
            ),
          ],
        ),
        if (lineItems.isEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 6.0),
            child: Text('No items added yet',
                style:
                    textTheme.bodyMedium?.copyWith(color: Palette.placeholder),
                textAlign: TextAlign.left),
          ),
      ],
    );
  }

  Widget _buildSummaryRow(
      BuildContext context, String label, double value, String currencyCode) {
    final textTheme = context.textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: textTheme.bodySmall?.copyWith(
                color: Palette.blackSecondary,
                letterSpacing: 0.1,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          const SizedBox(width: 12), // Space between label and value
          Text(
            CurrencyWidget.value(context, currencyCode, value),
            style: textTheme.bodySmall?.copyWith(
              letterSpacing: 0.1,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            textAlign: TextAlign.right,
          ),
        ],
      ),
    );
  }
}

class LogoUploadWidget extends ConsumerStatefulWidget {
  const LogoUploadWidget({super.key});

  @override
  ConsumerState<LogoUploadWidget> createState() => _LogoUploadWidgetState();
}

class _LogoUploadWidgetState extends ConsumerState<LogoUploadWidget> {
  String? _selectedImage;
  final _isLoading = ValueNotifier<bool>(false);
  final _disableImageUploadButton = ValueNotifier<bool>(true);
  final _uploadProgress = ValueNotifier<double>(0.0);
  final _isUploading = ValueNotifier<bool>(false);

  @override
  void dispose() {
    _isLoading.dispose();
    _disableImageUploadButton.dispose();
    _uploadProgress.dispose();
    _isUploading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          maxWidth: 380,
          minWidth: 320,
          maxHeight: 520,
        ),
        child: Material(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    IconButton(
                      icon: SvgPicture.asset(kCloseSvg),
                      onPressed: () => Navigator.of(context).pop(),
                      tooltip: 'Close',
                    ),
                    const Gap(8),
                    Text(
                      'Upload Logo',
                      style: textTheme.titleMedium?.copyWith(
                        color: Palette.k6B797C,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const Gap(8),
                Text(
                  'Supported: .jpg, .png, max 2MB',
                  style: textTheme.bodySmall?.copyWith(color: Palette.k6B797C),
                ),
                const Gap(16),
                Center(
                  child: Container(
                    width: 140,
                    height: 140,
                    decoration: BoxDecoration(
                      border: Border.all(color: Palette.stroke),
                      borderRadius: BorderRadius.circular(8),
                      color: Palette.kFCFCFC,
                    ),
                    child: _selectedImage != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.memory(
                              base64Decode(_selectedImage!),
                              fit: BoxFit.contain,
                            ),
                          )
                        : const Icon(Icons.add_photo_alternate, size: 48),
                  ),
                ),
                const Gap(16),
                Center(
                  child: ValueListenableBuilder<bool>(
                    valueListenable: _isUploading,
                    builder: (context, isUploading, _) {
                      return FilledButton(
                        onPressed: isUploading ? null : _pickImageWrapper,
                        child: _isLoading.value
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : const Text('Choose Image'),
                      );
                    },
                  ),
                ),
                const Gap(16),
                // Upload progress indicator
                ValueListenableBuilder<bool>(
                  valueListenable: _isUploading,
                  builder: (context, isUploading, _) {
                    if (!isUploading) return const SizedBox.shrink();

                    return Column(
                      children: [
                        ValueListenableBuilder<double>(
                          valueListenable: _uploadProgress,
                          builder: (context, progress, _) {
                            return Column(
                              children: [
                                LinearProgressIndicator(
                                  value: progress / 100,
                                  backgroundColor: Palette.stroke,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Palette.k6B797C,
                                  ),
                                ),
                                const Gap(8),
                                Text(
                                  'Uploading... ${progress.toInt()}%',
                                  style: textTheme.bodySmall?.copyWith(
                                    color: Palette.k6B797C,
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                        const Gap(16),
                      ],
                    );
                  },
                ),
                MultiValueListenableBuilder<bool, bool, bool>(
                  valueListenable1: _disableImageUploadButton,
                  valueListenable2: _isLoading,
                  valueListenable3: _isUploading,
                  builder: (context, disabled, loading, uploading, _) {
                    final isDisabled = disabled || loading || uploading!;
                    return FilledButton(
                      onPressed: isDisabled ? null : _uploadImage,
                      child: loading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(uploading! ? 'Uploading...' : 'Upload'),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _pickImageWrapper() {
    _pickImage();
  }

  Future<void> _pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.bytes != null) {
          // Check file size (2MB max)
          const int maxSizeInBytes = 2 * 1024 * 1024; // 2MB
          if (file.bytes!.lengthInBytes > maxSizeInBytes) {
            if (mounted) {
              Toast.error('File size must not be more than 2MB', context);
            }
            return;
          }

          setState(() {
            _selectedImage = base64Encode(file.bytes!);
            _disableImageUploadButton.value = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        Toast.error("Error picking image: $e", context);
      }
    }
  }

  void _uploadImage() {
    _performUpload();
  }

  Future<void> _performUpload() async {
    if (_selectedImage == null) return;

    _isUploading.value = true;
    _uploadProgress.value = 0.0;

    try {
      // Simulate progress updates during upload
      _simulateUploadProgress();

      final res = await ref.read(uploadLogoUseCaseProvider(_selectedImage!));

      res.when(
        success: (_) {
          _uploadProgress.value = 100.0;
          ref
              .read(advanceInvoiceControllerProvider.notifier)
              .fetchAdvanceInvoiceSettings(forced: true);

          // Small delay to show 100% completion
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              Navigator.of(context).pop();
            }
          });
        },
        failure: (e, _) {
          _isUploading.value = false;
          _uploadProgress.value = 0.0;
          if (mounted) {
            Toast.apiError(e, context);
          }
        },
      );
    } catch (e) {
      _isUploading.value = false;
      _uploadProgress.value = 0.0;
      if (mounted) {
        Toast.error("Upload failed: $e", context);
      }
    }
  }

  void _simulateUploadProgress() {
    // Simulate upload progress since we don't have real progress from the API
    Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!_isUploading.value) {
        timer.cancel();
        return;
      }

      if (_uploadProgress.value < 90) {
        _uploadProgress.value += 2.0;
      } else {
        timer.cancel();
      }
    });
  }
}
