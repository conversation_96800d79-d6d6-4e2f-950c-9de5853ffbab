import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:td_procurement/app/advance_invoice/data/models/retail_invoice.dart';
import 'package:td_procurement/app/order/widgets/orders_table.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/components/widgets/hoverable_container.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class AdvanceInvoiceTableWidget extends ConsumerStatefulWidget {
  final List<RetailInvoice> invoices;

  const AdvanceInvoiceTableWidget(this.invoices, {super.key});

  @override
  ConsumerState<AdvanceInvoiceTableWidget> createState() =>
      _AdvanceInvoiceTableWidgetState();
}

class _AdvanceInvoiceTableWidgetState
    extends ConsumerState<AdvanceInvoiceTableWidget> {
  final loading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: _buildInvoicesList(textTheme),
        ),
      ],
    );
  }

  Widget _buildInvoicesList(TextTheme textTheme) {
    return CustomScrollView(
      slivers: [
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) => _buildInvoiceItem(
              invoice: widget.invoices[index],
              index: index,
              textTheme: textTheme,
            ),
            childCount: widget.invoices.length,
          ),
        ),
      ],
    );
  }

  Widget _buildInvoiceItem({
    required RetailInvoice invoice,
    required int index,
    required TextTheme textTheme,
  }) {
    return HoverableContainer(
      index: index,
      builder: (isHovered) => InkWell(
        onTap: () => _handleInvoiceTap(invoice),
        child: _buildTableRow(invoice, isHovered, textTheme),
      ),
    );
  }

  Future<void> _handleInvoiceTap(RetailInvoice invoice) async {
    if (mounted) {
      context.goNamed(
        kAdvanceInvoiceSummaryRoute,
        pathParameters: {'id': invoice.invoiceNumber.toString()},
        extra: invoice,
      );
    }
  }

  Widget _buildTableRow(
      RetailInvoice invoice, bool isHovered, TextTheme textTheme) {
    return Table(
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      columnWidths: _getColumnWidths(),
      children: [
        TableRow(
          decoration: BoxDecoration(
            border: Border(bottom: BorderSide(color: Palette.stroke)),
          ),
          children: [
            Container(),
            _buildInvoiceNumberCell(invoice, textTheme),
            _buildAmountCell(invoice, textTheme),
            _buildIsFinancedCell(invoice, textTheme),
            _buildCreatedDateCell(invoice, textTheme),
            _buildStatusCell(invoice, textTheme),
            _buildMoreOptionsButton(invoice),
          ],
        ),
      ],
    );
  }

  Map<int, TableColumnWidth> _getColumnWidths() {
    return const {
      0: FlexColumnWidth(0.31),
      1: FlexColumnWidth(1.4), // Invoice Number
      2: FlexColumnWidth(1.4), // Amount
      3: FlexColumnWidth(2.0), // Recipient
      4: FlexColumnWidth(1.4), // Created Date
      5: FlexColumnWidth(1.2), // Status
      6: FlexColumnWidth(0.5), // Actions
    };
  }

  Widget _buildInvoiceNumberCell(RetailInvoice invoice, TextTheme textTheme) {
    return buildContentCell(
      Text(
        '${invoice.invoiceNumber}',
        style: textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
          color: Palette.blackSecondary,
        ),
      ),
      textTheme,
    );
  }

  Widget _buildAmountCell(RetailInvoice invoice, TextTheme textTheme) {
    return buildContentCell(
      CurrencyWidget.tableWidget(
        context,
        invoice.currency,
        invoice.total,
        mainAxisAlignment: MainAxisAlignment.start,
        extra: const Gap(4),
      ),
      textTheme,
    );
  }

  Widget _buildIsFinancedCell(RetailInvoice invoice, TextTheme textTheme) {
    return buildContentCell(
      Text(
        invoice.isAdvanced.toString(),
        style: textTheme.bodyMedium,
        overflow: TextOverflow.ellipsis,
      ),
      textTheme,
    );
  }

  Widget _buildCreatedDateCell(RetailInvoice invoice, TextTheme textTheme) {
    return buildContentCell(
      Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            invoice.createdAt.toDate(),
            style: textTheme.bodyMedium?.copyWith(
              color: Palette.blackSecondary,
            ),
          ),
          if (invoice.isAdvanced)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Palette.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'Financed',
                style: textTheme.bodySmall?.copyWith(
                  color: Palette.primary,
                  fontSize: 10,
                ),
              ),
            ),
        ],
      ),
      textTheme,
    );
  }

  Widget _buildStatusCell(RetailInvoice invoice, TextTheme textTheme) {
    Color statusColor;
    String statusText;

    switch (invoice.approvalStatus.toLowerCase()) {
      case 'approved':
        statusColor = Colors.green;
        statusText = 'Approved';
        break;
      case 'pending':
        statusColor = Colors.orange;
        statusText = 'Pending';
        break;
      case 'rejected':
        statusColor = Colors.red;
        statusText = 'Rejected';
        break;
      default:
        statusColor = Colors.grey;
        statusText = invoice.approvalStatus;
    }

    return buildContentCell(
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: statusColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          statusText,
          style: textTheme.bodySmall?.copyWith(
            color: statusColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      textTheme,
    );
  }

  Widget _buildMoreOptionsButton(RetailInvoice invoice) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert, size: 20),
      onSelected: (value) => _handleMenuAction(value, invoice),
      itemBuilder: (context) => _buildMenuItems(invoice),
    );
  }

  void _handleMenuAction(String action, RetailInvoice invoice) {
    switch (action) {
      case 'view':
        _handleInvoiceTap(invoice);
        break;
      case 'copy':
        {
          Clipboard.setData(
              ClipboardData(text: invoice.invoiceNumber.toString()));
          Toast.show("Invoice number copied to clipboard", context,
              duration: 2, title: '');
        }
        break;
    }
  }

  List<PopupMenuItem<String>> _buildMenuItems(RetailInvoice invoice) {
    return [
      const PopupMenuItem(
        value: 'view',
        child: ListTile(
          leading: Icon(Icons.visibility_outlined),
          title: Text(
            'View Invoice',
            style: TextStyle(color: Colors.black),
          ),
        ),
      ),
      const PopupMenuItem(
        value: 'copy',
        child: ListTile(
          leading: Icon(Icons.copy_outlined),
          title: Text(
            'Copy Invoice Number',
            style: TextStyle(color: Colors.black),
          ),
        ),
      ),
    ];
  }
}
