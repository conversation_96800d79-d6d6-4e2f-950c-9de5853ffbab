import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/advance_invoice/data/models/discount.dart';
import 'package:td_procurement/app/advance_invoice/data/models/line_item.dart';
import 'package:td_procurement/app/advance_invoice/data/models/retail_invoice.dart';
import 'package:td_procurement/app/advance_invoice/presentation/logic/advance_invoice_controller.dart';
import 'package:td_procurement/app/advance_invoice/presentation/views/widgets/advance_invoice_summary_table.dart';
import 'package:td_procurement/app/advance_invoice/presentation/views/widgets/advance_invoice_tracking.dart';
import 'package:td_procurement/app/order/widgets/order_action_bar.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class AdvanceInvoiceSummaryScreen extends ConsumerStatefulWidget {
  const AdvanceInvoiceSummaryScreen(this.invoiceId, {super.key, this.invoice});

  final String invoiceId;
  final RetailInvoice? invoice;

  @override
  ConsumerState<AdvanceInvoiceSummaryScreen> createState() =>
      _AdvanceInvoiceSummaryScreenState();
}

class _AdvanceInvoiceSummaryScreenState
    extends ConsumerState<AdvanceInvoiceSummaryScreen> {
  final rightButton1 = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    prepInvoiceData();
  }

  @override
  void dispose() {
    rightButton1.dispose();
    super.dispose();
  }

  void prepInvoiceData() {
    // Only fetch if invoice is not provided
    if (widget.invoice == null) {
      Future.microtask(() {
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .fetchOrderDetails(widget.invoiceId);
      });
    } else {
      // Set the provided invoice in the state
      Future.microtask(() {
        ref
            .read(advanceInvoiceControllerProvider.notifier)
            .setAdvanceInvoiceDetails(widget.invoice!);
      });
    }
  }

  AsyncValue<RetailInvoice?> get invoiceData =>
      ref.watch(advanceInvoiceControllerProvider).advanceInvoiceDetails;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Skeletonizer(
        enabled: invoiceData.isLoading,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildActionBar(),
            Expanded(
              child: SingleChildScrollView(
                child: invoiceData.when(
                  skipLoadingOnReload: true,
                  data: (invoice) => _buildInvoiceContent(invoice),
                  loading: _loadingWidget,
                  error: (e, s) {
                    if (kIsWeb || kIsWasm) return _loadingWidget();
                    return FailureWidget(
                      fullScreen: true,
                      heightFactor: 0.7,
                      e: e,
                      retry: prepInvoiceData,
                    );
                  },
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _loadingWidget() {
    return Skeletonizer(
      enabled: true,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 40),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 5,
              child: Container(
                height: 552,
                color: Colors.white,
              ),
            ),
            const Gap(10),
            Expanded(
              flex: 3,
              child: Padding(
                padding: const EdgeInsets.only(top: 30),
                child: Container(
                  height: 552,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionBar() {
    return ValueListenableBuilder<bool>(
      valueListenable: rightButton1,
      builder: (context, loading1, _) {
        return OrderActionBarWidget(
          leftText: 'Invoice Details',
          isCloseIcon: false,
          leftIconAction: () => context.pop(),
          rightButton1Text: 'Print',
          rightButton1Loading: loading1,
          rightButton1Action: _handlePrint,
        );
      },
    );
  }

  Widget _buildInvoiceContent(RetailInvoice? invoice) {
    if (invoice == null) {
      return const Center(
        child: Text('Invoice not found'),
      );
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 5,
                child: AdvanceInvoiceSummaryTableWidget(invoice),
              ),
              const Gap(10),
              Expanded(
                flex: 3,
                child: Padding(
                  padding: const EdgeInsets.only(top: 30),
                  child: AdvanceInvoiceTrackingWidget(invoice),
                ),
              ),
            ],
          ),
          // const Gap(24),
          // if (invoice.note?.isNotEmpty ?? false) ...[
          //   _buildNotesSection(invoice),
          //   const Gap(24),
          // ],
          // _buildBankDetailsSection(invoice),
          // const Gap(24),
          // _buildShippingAddressSection(invoice),
        ],
      ),
    );
  }

  Widget _buildInvoiceHeader(RetailInvoice invoice) {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Palette.stroke),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Palette.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.receipt_long,
                  color: Palette.primary,
                  size: 28,
                ),
              ),
              const Gap(16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'INVOICE',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                            letterSpacing: 1.2,
                          ),
                    ),
                    const Gap(4),
                    Text(
                      '#${invoice.invoiceNumber.toString().padLeft(8, '0')}',
                      style:
                          Theme.of(context).textTheme.headlineMedium?.copyWith(
                                color: Palette.primaryBlack,
                                fontWeight: FontWeight.bold,
                                fontSize: 28,
                              ),
                    ),
                  ],
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: _getStatusColor(invoice.approvalStatus)
                      .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _getStatusColor(invoice.approvalStatus)
                        .withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  invoice.approvalStatus.toUpperCase(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getStatusColor(invoice.approvalStatus),
                        fontWeight: FontWeight.w600,
                        letterSpacing: 0.5,
                      ),
                ),
              ),
            ],
          ),
          const Gap(24),
          Row(
            children: [
              Expanded(
                child: _buildHeaderInfoItem(
                  'Created Date',
                  invoice.createdAt.toFullDate(),
                  Icons.calendar_today,
                ),
              ),
              const Gap(24),
              Expanded(
                child: _buildHeaderInfoItem(
                  'Total Amount',
                  CurrencyWidget.value(
                      context, invoice.currency.iso!, invoice.total),
                  Icons.account_balance_wallet,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderInfoItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: Colors.grey[600],
                size: 16,
              ),
              const Gap(8),
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ),
          const Gap(8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Palette.primaryBlack,
                  fontWeight: FontWeight.w600,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceOverview(RetailInvoice invoice) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Palette.stroke),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Palette.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.info_outline,
                  color: Palette.primary,
                  size: 20,
                ),
              ),
              const Gap(12),
              Text(
                'Invoice Overview',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Palette.primaryBlack,
                    ),
              ),
            ],
          ),
          const Gap(24),
          Row(
            children: [
              Expanded(
                child: _buildOverviewCard(
                  'Subtotal',
                  CurrencyWidget.value(
                      context, invoice.currency.iso!, invoice.subtotal),
                  Icons.calculate,
                  Colors.blue,
                ),
              ),
              const Gap(16),
              Expanded(
                child: _buildOverviewCard(
                  'Tax Rate',
                  '${invoice.taxRate}%',
                  Icons.receipt,
                  Colors.orange,
                ),
              ),
              const Gap(16),
              Expanded(
                child: _buildOverviewCard(
                  'Financed',
                  invoice.isAdvanced ? 'Yes' : 'No',
                  Icons.account_balance,
                  invoice.isAdvanced ? Colors.green : Colors.grey,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewCard(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 16,
                ),
              ),
              const Gap(8),
              Expanded(
                child: Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ),
            ],
          ),
          const Gap(12),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Palette.primaryBlack,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemsSection(RetailInvoice invoice) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Palette.stroke),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Palette.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.inventory_2,
                  color: Palette.primary,
                  size: 20,
                ),
              ),
              const Gap(12),
              Text(
                'Items (${invoice.items.length})',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Palette.primaryBlack,
                    ),
              ),
            ],
          ),
          const Gap(24),
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: Text(
                          'Item',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey[700],
                                  ),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          'Qty',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey[700],
                                  ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          'Price',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey[700],
                                  ),
                          textAlign: TextAlign.right,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          'Total',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey[700],
                                  ),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                ),
                // Items
                ...invoice.items.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  final isLast = index == invoice.items.length - 1;

                  return Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: isLast
                          ? null
                          : Border(
                              bottom: BorderSide(color: Colors.grey[200]!),
                            ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                item.name,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w500,
                                      color: Palette.primaryBlack,
                                    ),
                              ),
                              if (item.taxRate > 0) ...[
                                const Gap(4),
                                Text(
                                  'Tax: ${item.taxRate}%',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        Expanded(
                          child: Text(
                            item.quantity.toString(),
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            CurrencyWidget.value(
                                context, invoice.currency.iso!, item.price),
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            CurrencyWidget.value(context, invoice.currency.iso!,
                                _calculateItemTotal(item, invoice)),
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection(RetailInvoice invoice) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Palette.stroke),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Palette.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.calculate,
                  color: Palette.primary,
                  size: 20,
                ),
              ),
              const Gap(12),
              Text(
                'Summary',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Palette.primaryBlack,
                    ),
              ),
            ],
          ),
          const Gap(24),
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              children: [
                _buildSummaryRow(
                    'Subtotal', invoice.subtotal, invoice.currency.iso!),
                const Gap(12),
                _buildSummaryRow(
                    'Tax', invoice.totalTax, invoice.currency.iso!),
                const Gap(12),
                _buildSummaryRow(
                    'Shipping', invoice.shippingCost, invoice.currency.iso!),
                const Gap(12),
                _buildSummaryRow('Processing', invoice.processingCost,
                    invoice.currency.iso!),
                if (invoice.discount != null) ...[
                  const Gap(12),
                  _buildSummaryRow('Discount', -invoice.discount!.value,
                      invoice.currency.iso!,
                      isDiscount: true),
                ],
                const Gap(16),
                Container(
                  height: 1,
                  color: Colors.grey[300],
                ),
                const Gap(16),
                _buildSummaryRow('Total', invoice.total, invoice.currency.iso!,
                    isTotal: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, num amount, String currencyCode,
      {bool isTotal = false, bool isDiscount = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
                color: isTotal ? Palette.primaryBlack : Colors.grey[700],
              ),
        ),
        Text(
          CurrencyWidget.value(context, currencyCode, amount),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
                color: Palette.primaryBlack,
              ),
        ),
      ],
    );
  }

  Widget _buildNotesSection(RetailInvoice invoice) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Palette.stroke),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Palette.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.note,
                  color: Palette.primary,
                  size: 20,
                ),
              ),
              const Gap(12),
              Text(
                'Notes',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Palette.primaryBlack,
                    ),
              ),
            ],
          ),
          const Gap(16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Text(
              invoice.note ?? '',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.blue[800],
                    height: 1.5,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBankDetailsSection(RetailInvoice invoice) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Palette.stroke),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Palette.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.account_balance,
                  color: Palette.primary,
                  size: 20,
                ),
              ),
              const Gap(12),
              Text(
                'Bank Details',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Palette.primaryBlack,
                    ),
              ),
            ],
          ),
          const Gap(24),
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green[200]!),
            ),
            child: Column(
              children: [
                _buildBankDetailRow(
                    'Bank Name', invoice.bankAccount.bankName ?? '-'),
                const Gap(12),
                _buildBankDetailRow(
                    'Account Name', invoice.bankAccount.accountName ?? '-'),
                const Gap(12),
                _buildBankDetailRow(
                    'Account Number', invoice.bankAccount.accountNumber ?? '-'),
                if (invoice.bankAccount.bankCode != null) ...[
                  const Gap(12),
                  _buildBankDetailRow(
                      'Bank Code', invoice.bankAccount.bankCode!),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBankDetailRow(String label, String value) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.green[800],
                ),
          ),
        ),
      ],
    );
  }

  Widget _buildShippingAddressSection(RetailInvoice invoice) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Palette.stroke),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Palette.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.location_on,
                  color: Palette.primary,
                  size: 20,
                ),
              ),
              const Gap(12),
              Text(
                'Shipping Address',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Palette.primaryBlack,
                    ),
              ),
            ],
          ),
          const Gap(24),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  invoice.shippingAddress.fullName ?? '-',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Palette.primaryBlack,
                      ),
                ),
                const Gap(8),
                Text(
                  invoice.shippingAddress.address1 ?? '-',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[700],
                      ),
                ),
                const Gap(4),
                Text(
                  '${invoice.shippingAddress.lga}, ${invoice.shippingAddress.state}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[700],
                      ),
                ),
                const Gap(4),
                Text(
                  invoice.shippingAddress.country ?? '-',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[700],
                      ),
                ),
                if (invoice.shippingAddress.phone != null) ...[
                  const Gap(8),
                  Row(
                    children: [
                      Icon(
                        Icons.phone,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const Gap(8),
                      Text(
                        invoice.shippingAddress.phone!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[700],
                            ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handlePrint() {
    if (invoiceData.hasValue && invoiceData.value != null) {
      // TODO: Implement print functionality
      rightButton1.value = true;
      Future.delayed(const Duration(seconds: 2), () {
        rightButton1.value = false;
      });
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  num _calculateItemTotal(LineItem item, RetailInvoice invoice) {
    // Calculate base total (quantity * price)
    num baseTotal = item.quantity * item.price;

    // Apply item discount if available
    if (item.discount != null) {
      if (item.discount!.type == DiscountType.fixed) {
        baseTotal -= item.discount!.value;
      } else if (item.discount!.type == DiscountType.percentage) {
        baseTotal -= (baseTotal * item.discount!.value / 100);
      }
    }

    // Apply item tax
    if (item.taxRate > 0) {
      baseTotal += (baseTotal * item.taxRate / 100);
    }

    return baseTotal;
  }
}
