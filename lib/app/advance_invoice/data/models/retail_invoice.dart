import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/models/address.dart';
import 'package:td_commons_flutter/models/currency.dart';
import 'package:td_commons_flutter/utils/methods.dart';

import 'index.dart';

class RetailInvoice extends Equatable {
  final String id;
  final String issuerOutletId;
  final String recipientOutletId;
  final String? note;
  final List<LineItem> items;
  final num shippingCost;
  final num processingCost;
  final num taxRate;
  final num subtotal;
  final num totalTax;
  final num total;
  final Discount? discount;
  final bool isAdvanced;
  final WalletBank bankAccount;
  final Currency currency;
  final String approvalStatus;
  final DateTime createdAt;
  final Address shippingAddress;
  final int invoiceNumber;

  const RetailInvoice({
    required this.id,
    required this.issuerOutletId,
    required this.recipientOutletId,
    this.note,
    required this.items,
    required this.shippingCost,
    required this.processingCost,
    required this.taxRate,
    required this.subtotal,
    required this.totalTax,
    required this.total,
    this.discount,
    required this.isAdvanced,
    required this.bankAccount,
    required this.currency,
    required this.approvalStatus,
    required this.createdAt,
    required this.shippingAddress,
    required this.invoiceNumber,
  });

  RetailInvoice copyWith({
    String? id,
    String? issuerOutletId,
    String? recipientOutletId,
    String? note,
    List<LineItem>? items,
    num? shippingCost,
    num? processingCost,
    num? taxRate,
    num? subtotal,
    num? totalTax,
    num? total,
    Discount? discount,
    bool? isAdvanced,
    WalletBank? bankAccount,
    Currency? currency,
    String? approvalStatus,
    DateTime? createdAt,
    Address? shippingAddress,
    int? invoiceNumber,
  }) {
    return RetailInvoice(
      id: id ?? this.id,
      issuerOutletId: issuerOutletId ?? this.issuerOutletId,
      recipientOutletId: recipientOutletId ?? this.recipientOutletId,
      note: note ?? this.note,
      items: items ?? this.items,
      shippingCost: shippingCost ?? this.shippingCost,
      processingCost: processingCost ?? this.processingCost,
      taxRate: taxRate ?? this.taxRate,
      subtotal: subtotal ?? this.subtotal,
      totalTax: totalTax ?? this.totalTax,
      total: total ?? this.total,
      discount: discount ?? this.discount,
      isAdvanced: isAdvanced ?? this.isAdvanced,
      bankAccount: bankAccount ?? this.bankAccount,
      currency: currency ?? this.currency,
      approvalStatus: approvalStatus ?? this.approvalStatus,
      createdAt: createdAt ?? this.createdAt,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'issuerOutletId': issuerOutletId,
      'recipientOutletId': recipientOutletId,
      'note': note,
      'items': items.map((x) => x.toMap()).toList(),
      'shippingCost': shippingCost,
      'processingCost': processingCost,
      'taxRate': taxRate,
      'subtotal': subtotal,
      'totalTax': totalTax,
      'total': total,
      'discount': discount?.toMap(),
      'isAdvanced': isAdvanced,
      'bankAccount': bankAccount.toMap(),
      'currency': currency.toMap(),
      'approvalStatus': approvalStatus,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'shippingAddress': shippingAddress.toMap(),
      'invoiceNumber': invoiceNumber,
    };
  }

  factory RetailInvoice.fromMap(Map<String, dynamic> map) {
    return RetailInvoice(
      id: map['_id'] ?? map['id'] ?? '',
      issuerOutletId: map['issuerOutletId'],
      recipientOutletId: map['recipientOutletId'],
      note: map['note'],
      items: List<LineItem>.from(
        (map['items'] as List).map<LineItem>(
          (x) => LineItem.fromMap(x as Map<String, dynamic>),
        ),
      ),
      shippingCost: map['shippingCost'] ?? 0,
      processingCost: map['processingCost'] ?? 0,
      taxRate: map['taxRate'] ?? 0,
      subtotal: map['subtotal'] ?? 0,
      totalTax: map['totalTax'] ?? 0,
      total: map['total'] ?? 0,
      discount: map['discount'] != null
          ? Discount.fromMap(map['discount'] as Map<String, dynamic>)
          : null,
      isAdvanced: map['isAdvanced'] ?? false,
      bankAccount:
          WalletBank.fromMap(map['bankAccount'] as Map<String, dynamic>),
      currency: Currency.fromMap(map['currency'] as Map<String, dynamic>),
      approvalStatus: map['approvalStatus'] ?? '',
      createdAt: parseDate(map['createdAt'])!,
      shippingAddress:
          Address.fromMap(map['shippingAddress'] as Map<String, dynamic>),
      invoiceNumber: map['invoiceNumber'],
    );
  }

  @override
  List<Object?> get props {
    return [
      id,
      issuerOutletId,
      recipientOutletId,
      note,
      items,
      shippingCost,
      processingCost,
      taxRate,
      subtotal,
      totalTax,
      total,
      discount,
      isAdvanced,
      bankAccount,
      currency,
      approvalStatus,
      createdAt,
      shippingAddress,
      invoiceNumber,
    ];
  }

  String toJson() => json.encode(toMap());

  factory RetailInvoice.fromJson(String source) =>
      RetailInvoice.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  factory RetailInvoice.defaultValue() {
    return RetailInvoice(
      id: 'id',
      issuerOutletId: 'issuerOutletId',
      recipientOutletId: 'recipientOutletId',
      note: 'note',
      items: List.filled(5, LineItem.defaultValue()),
      shippingCost: 0,
      processingCost: 0,
      taxRate: 0,
      subtotal: 0,
      totalTax: 0,
      total: 0,
      discount: null,
      isAdvanced: false,
      bankAccount: WalletBank.defaultValue(),
      currency: Currency(iso: 'NGN', symbol: '₦'),
      approvalStatus: 'approvalStatus',
      createdAt: DateTime.now(),
      shippingAddress: Address(
        id: 'id',
        company: 'company',
        isBillingDefault: false,
        isShippingDefault: false,
        lga: 'lga',
        state: 'state',
        phone: 'phone',
        address1: 'address1',
        fullName: 'fullName',
        country: 'country',
      ),
      invoiceNumber: 0123456,
    );
  }
}
