import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/order/order_params.dart';
import 'package:td_procurement/core/router/routes.dart';
import 'package:td_procurement/src/components/widgets/index.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class EmptyOrdersWidget extends ConsumerWidget {
  const EmptyOrdersWidget(this.orderType, {super.key});

  final OrderType orderType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final path = orderType == OrderType.sales
        ? kCreateSalesOrderRoute
        : kCreateOrderRoute;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        EmptyWidget(
          icon: '$kSvgDir/order/box.svg',
          routeName: path,
          title: 'No ${orderType.name}s',
          routeInfo: 'Create a new order →',
          subTitle: 'There are no orders matching your filter criteria',
        ),
      ],
    );
  }
}
