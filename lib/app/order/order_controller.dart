import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_commons_flutter/models/orders.dart';
import 'package:td_commons_flutter/models/retail_branch.dart';
import 'package:td_commons_flutter/models/variant.dart';
import 'package:td_procurement/app/auth/domain/logic/controllers/user_controller.dart';
import 'package:td_procurement/app/order/cart_notifier.dart';
import 'package:td_procurement/app/order/order_state.dart';
import 'package:td_procurement/core/models/index.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

import 'order_params.dart';
import 'order_service.dart';

final orderControllerProvider =
    NotifierProvider<OrderController, OrderState>(() => OrderController());

class OrderController extends Notifier<OrderState> {
  late OrderService _orderService;

  @override
  OrderState build() {
    _orderService = ref.read(orderServiceProvider);

    // Watch user state to ensure controller rebuilds when user changes
    final user = ref.watch(userControllerProvider);

    // If user is null (logged out), return initial state
    if (user == null) {
      return OrderState.initial();
    }

    return OrderState.initial();
  }

  /// Computed property to merge `collections` , `exclusiveCollections` and `outletVariants`
  /// A single value to be used for non export countries
  AsyncValue<List<Variant>> get nonExportVariants {
    final collectionsValue = state.collections;
    // final exclusiveCollectionValue = state.exclusiveCollections;
    final outletVariantsValue = state.outletVariants;

    // Handle loading states
    if (collectionsValue is AsyncLoading &&
        // exclusiveCollectionValue is AsyncLoading &&
        outletVariantsValue is AsyncLoading) {
      return const AsyncLoading();
    }

    // Handle error states
    if (collectionsValue is AsyncError) {
      return AsyncError(collectionsValue.error!, collectionsValue.stackTrace!);
    }
    // if (exclusiveCollectionValue is AsyncError) {
    //   return AsyncError(exclusiveCollectionValue.error!,
    //       exclusiveCollectionValue.stackTrace!);
    // }
    if (outletVariantsValue is AsyncError) {
      return AsyncError(
          outletVariantsValue.error!, outletVariantsValue.stackTrace!);
    }

    // Handle data states
    if (collectionsValue is AsyncData &&
        // exclusiveCollectionValue is AsyncData &&
        outletVariantsValue is AsyncData) {
      final List<Variant> collectionVariants = collectionsValue.value!
          .expand((x) => x.variants ?? [])
          .whereType<Variant>()
          .toList();
      // final List<Variant> exclusiveVariants = exclusiveCollectionValue.value!
      //     .expand((x) => x.variants ?? [])
      //     .whereType<Variant>()
      //     .toList();
      final outletVariants =
          outletVariantsValue.value!.whereType<Variant>().toList();
      final allVariants = [
        // ...exclusiveVariants,
        ...collectionVariants,
        ...outletVariants
      ];
      return AsyncData(allVariants);
    }

    if ((collectionsValue is AsyncData && collectionsValue.value!.isNotEmpty) ||
        // (exclusiveCollectionValue is AsyncData &&
        //     exclusiveCollectionValue.value!.isNotEmpty) ||
        (outletVariantsValue is AsyncData &&
            outletVariantsValue.value!.isNotEmpty)) {
      final List<Variant> collectionVariants = collectionsValue.value != null
          ? collectionsValue.value!
              .expand((x) => x.variants ?? [])
              .whereType<Variant>()
              .toList()
          : [];
      // final List<Variant> exclusiveVariants =
      //     exclusiveCollectionValue.value != null
      //         ? exclusiveCollectionValue.value!
      //             .expand((x) => x.variants ?? [])
      //             .whereType<Variant>()
      //             .toList()
      //         : [];
      final List<Variant> outletVariants = outletVariantsValue.value != null
          ? outletVariantsValue.value!.whereType<Variant>().toList()
          : [];
      final allVariants = [
        // ...exclusiveVariants,
        ...collectionVariants,
        ...outletVariants
      ];
      return AsyncData(allVariants);
    }

    // Fallback for unknown states
    return const AsyncLoading();
  }

  AsyncValue<List<Variant>> get exclusiveVariants {
    final exclusiveCollectionValue = state.exclusiveCollections;

    // Handle loading states
    if (exclusiveCollectionValue is AsyncLoading) {
      return const AsyncLoading();
    }

    // Handle error states
    if (exclusiveCollectionValue is AsyncError) {
      return AsyncError(exclusiveCollectionValue.error!,
          exclusiveCollectionValue.stackTrace!);
    }

    // Handle data states
    if (exclusiveCollectionValue is AsyncData) {
      final List<Variant> exclusiveVariants = exclusiveCollectionValue.value!
          .expand((x) => x.variants ?? [])
          .whereType<Variant>()
          .toList();
      return AsyncData(exclusiveVariants);
    }

    if ((exclusiveCollectionValue is AsyncData &&
        exclusiveCollectionValue.value!.isNotEmpty)) {
      final List<Variant> exclusiveVariants =
          exclusiveCollectionValue.value != null
              ? exclusiveCollectionValue.value!
                  .expand((x) => x.variants ?? [])
                  .whereType<Variant>()
                  .toList()
              : [];

      return AsyncData(exclusiveVariants);
    }

    // Fallback for unknown states
    return const AsyncLoading();
  }

  List<String> get categoriesFilter {
    return ref.read(countryTypeProvider).when(
      export: () {
        return state.catalogCategoryOptions;
      },
      nonExport: () {
        final mergedVariants =
            ref.read(orderControllerProvider.notifier).nonExportVariants;

        if (mergedVariants is AsyncData) {
          final variants = mergedVariants.value!;
          return ProductsManager(variants).getCategoryFilter();
        }

        return [];
      },
    );
  }

  List<String> get brandsFilter {
    return ref.read(countryTypeProvider).when(
      export: () {
        return state.catalogBrandOptions;
      },
      nonExport: () {
        final mergedVariants =
            ref.read(orderControllerProvider.notifier).nonExportVariants;

        if (mergedVariants is AsyncData) {
          final variants = mergedVariants.value!;
          return ProductsManager(variants).getBrandFilter();
        }

        return [];
      },
    );
  }

  List<String> get exclusivesCategoriesFilter {
    return ref.read(countryTypeProvider).when(
      export: () {
        return state.catalogCategoryOptions;
      },
      nonExport: () {
        final exclusiveCollections = state.exclusiveCollections;

        if (exclusiveCollections is AsyncData) {
          List<Variant> variants = [];

          final showingOtherProducts =
              ref.read(cartProvider).showingOtherProducts;

          variants = exclusiveCollections.value!
              .expand((x) => x.variants ?? [])
              .whereType<Variant>()
              .toList();

          if (showingOtherProducts) {
            final otherVariants =
                ref.read(orderControllerProvider.notifier).nonExportVariants;
            if (otherVariants is AsyncData) {
              variants = [...variants, ...otherVariants.value!];
            }
          }

          return ProductsManager(variants).getCategoryFilter();
        }

        return [];
      },
    );
  }

  List<String> get exclusivesBrandsFilter {
    return ref.read(countryTypeProvider).when(
      export: () {
        return state.catalogBrandOptions;
      },
      nonExport: () {
        final exclusiveCollections = state.exclusiveCollections;

        if (exclusiveCollections is AsyncData) {
          final variants = exclusiveCollections.value!
              .expand((x) => x.variants ?? [])
              .whereType<Variant>()
              .toList();
          return ProductsManager(variants).getBrandFilter();
        }

        return [];
      },
    );
  }

  Future<void> fetchTransactions(
    FetchTransactionsParam params, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    state = state.copyWith(
        fetchTransactionsParam: state.fetchTransactionsParam
            .copyWith(activeIndex: params.activeIndex));

    if (forced) {
      state = state.copyWith(transactions: const AsyncLoading());
    } else {
      if (params.currentPage == 1 &&
          (state.transactions.hasValue &&
              state.transactions.value!.isNotEmpty)) {
        return;
      }

      // return if query has reached the max page or if loading or loading more orders
      // if (state.fetchTransactionsParam.currentPage ==
      //         state.fetchTransactionsParam.totalPages ||
      //     state.transactions.isLoading ||
      //     state.fetchTransactionsParam.loadingMore) {
      //   return;
      // }

      if (state.fetchTransactionsParam.loaded) {
        state = state.copyWith(
            fetchTransactionsParam:
                state.fetchTransactionsParam.copyWith(loadingMore: true));
      } else {
        state = state.copyWith(transactions: const AsyncLoading());
      }
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _orderService.fetchOrders(params);

      res.when(
        success: (data) {
          state = state.copyWith(
            transactions:
                AsyncData([...?state.transactions.value, ...data.transactions]),
            fetchTransactionsParam: params
                .fromQueryParams(data.queryParams)
                .copyWith(loaded: true, loadingMore: false),
          );
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            if (!state.fetchTransactionsParam.loaded) {
              state = state.copyWith(
                  transactions: AsyncError(e, StackTrace.current),
                  fetchTransactionsParam: state.fetchTransactionsParam
                      .copyWith(loaded: false, loadingMore: false));
            }
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> fetchOrderDetails(
    String orderId, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(orderDetails: const AsyncLoading());
    } else {
      state = state.copyWith(orderDetails: const AsyncLoading());
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _orderService.fetchOrderDetails(orderId);

      res.when(
        success: (data) {
          state = state.copyWith(
            orderDetails: AsyncData(data),
            showShipmentError: data.shipment == null,
            transactionInView: state.transactionInView ?? data.transaction,
          );
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(
                orderDetails: AsyncError(e, StackTrace.current),
                showOrderDetailsError: true);
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> fetchExportVariants({
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(exportVariants: const AsyncLoading());
    } else if (state.exportVariants.hasValue &&
        state.exportVariants.value!.isNotEmpty) {
      return;
    } else {
      state = state.copyWith(exportVariants: const AsyncLoading());
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _orderService.fetchExportVariants();
      res.when(
        success: (data) {
          final pm = ProductsManager(data);
          state = state.copyWith(
              exportVariants: AsyncData(data),
              catalogCategoryOptions: pm.getCategoryFilter(),
              catalogBrandOptions: pm.getBrandFilter());
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(
                exportVariants: AsyncError(e, StackTrace.current));
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  // Future<void> fetchCollectionsAndVariants(
  //   String hexCode, {
  //   int retryCount = 0,
  //   int delaySeconds = 2,
  //   bool forced = false,
  // }) async {
  //   await Future.wait([
  //     fetchCollections(hexCode,
  //         retryCount: retryCount, delaySeconds: delaySeconds, forced: forced),
  //     fetchOutletVariants(
  //         retryCount: retryCount, delaySeconds: delaySeconds, forced: forced),
  //   ]);
  // }

  Future<void> fetchCollections(
    String hexCode, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    _fetchExclusive(hexCode,
        retryCount: retryCount, delaySeconds: delaySeconds, forced: forced);
    _fetchOthers(hexCode,
        retryCount: retryCount, delaySeconds: delaySeconds, forced: forced);
  }

  Future<void> _fetchExclusive(
    String hexCode, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(exclusiveCollections: const AsyncLoading());
    } else if (state.exclusiveCollections.hasValue &&
        state.exclusiveCollections.value!.isNotEmpty) {
      return;
    } else {
      state = state.copyWith(exclusiveCollections: const AsyncLoading());
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _orderService.fetchNonExportCollections(hexCode, true);
      res.when(
        success: (data) {
          state = state.copyWith(exclusiveCollections: AsyncData(data));
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(
                exclusiveCollections: AsyncError(e, StackTrace.current));
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> _fetchOthers(
    String hexCode, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(collections: const AsyncLoading());
    } else if (state.collections.hasValue &&
        state.collections.value!.isNotEmpty) {
      return;
    } else {
      state = state.copyWith(collections: const AsyncLoading());
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _orderService.fetchNonExportCollections(hexCode, false);
      res.when(
        success: (data) {
          state = state.copyWith(collections: AsyncData(data));
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state =
                state.copyWith(collections: AsyncError(e, StackTrace.current));
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> fetchOutletVariants({
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(outletVariants: const AsyncLoading());
    } else if (state.outletVariants.hasValue &&
        state.outletVariants.value!.isNotEmpty) {
      return;
    } else {
      state = state.copyWith(outletVariants: const AsyncLoading());
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _orderService.fetchNonExportOutletVariants();
      res.when(
        success: (data) {
          state = state.copyWith(outletVariants: AsyncData(data));
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(
                outletVariants: AsyncError(e, StackTrace.current));
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<List<Variant>> searchOutletVariants(
    String hexCode,
    String searchTerm, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;
    List<Variant> variants = [];

    state = state.copyWith(searchedVariants: const AsyncLoading());

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _orderService.searchNonExportOutletVariants(
          hexCode, searchTerm);
      res.when(
        success: (data) {
          variants = data;
          state = state.copyWith(searchedVariants: AsyncData(data));
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(
                searchedVariants: AsyncError(e, StackTrace.current));
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }

    return variants;
  }

  Future<void> fetchSalesOrders(
    FetchSalesOrdersParams params, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    state = state.copyWith(
        fetchSalesOrdersParams: state.fetchSalesOrdersParams
            .copyWith(activeIndex: params.activeIndex));

    if (forced) {
      state = state.copyWith(salesOrders: const AsyncLoading());
    } else {
      if (params.currentPage == 1 &&
          (state.salesOrders.hasValue && state.salesOrders.value!.isNotEmpty)) {
        return;
      }

      // return if query has reached the max page or if loading or loading more orders
      // if (state.fetchSalesOrdersParams.currentPage ==
      //         state.fetchSalesOrdersParams.totalPages ||
      //     state.salesOrders.isLoading ||
      //     state.fetchSalesOrdersParams.loadingMore) {
      //   return;
      // }

      if (state.fetchSalesOrdersParams.loaded) {
        state = state.copyWith(
            fetchSalesOrdersParams:
                state.fetchSalesOrdersParams.copyWith(loadingMore: true));
      } else {
        state = state.copyWith(salesOrders: const AsyncLoading());
      }
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _orderService.fetchSalesOrders(params);

      res.when(
        success: (data) {
          state = state.copyWith(
            salesOrders:
                AsyncData([...?state.salesOrders.value, ...data.orders]),
            fetchSalesOrdersParams: params
                .fromQueryParams(data.queryParams)
                .copyWith(loaded: true, loadingMore: false),
          );
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            if (!state.fetchSalesOrdersParams.loaded) {
              state = state.copyWith(
                  salesOrders: AsyncError(e, StackTrace.current),
                  fetchSalesOrdersParams: state.fetchSalesOrdersParams
                      .copyWith(loaded: false, loadingMore: false));
            }
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> fetchSalesLocationDrivers(
    FetchSalesLocationDriverParams params, {
    int retryCount = 0,
    int delaySeconds = 2,
    forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    if (forced) {
      state = state.copyWith(salesLocationDrivers: const AsyncLoading());
    } else if (state.salesLocationDrivers is AsyncData &&
        state.salesLocationDrivers.valueOrNull != null) {
      return;
    } else {
      state = state.copyWith(salesLocationDrivers: const AsyncLoading());
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await _orderService.fetchSalesLocationDrivers(params);

      res.when(
        success: (data) {
          state = state.copyWith(salesLocationDrivers: AsyncData(data.drivers));
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(
                salesLocationDrivers: AsyncError(e, StackTrace.current));
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  // Methods below handle local state updates for the order controller
  // These do not make API calls and only modify the controller's state
  Future<void> setTransactionInView(Transaction transaction) async {
    state = state.copyWith(transactionInView: transaction);
    return;
  }

  Future<void> setSalesOrderInView(Order salesOrder) async {
    state = state.copyWith(salesOrderInView: salesOrder);
    return;
  }

  Transaction? transaction(String id) {
    if (state.transactions.hasValue) {
      return state.transactions.valueOrNull
          ?.firstWhereOrNull((x) => x.id == id);
    }
    return null;
  }

  Order? salesOrder(String id) {
    if (state.salesOrders.hasValue) {
      return state.salesOrders.valueOrNull?.firstWhereOrNull((x) => x.id == id);
    }
    return null;
  }

  void setDateFilter(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(
        selectedStartDate: Optional(startDate),
        selectedEndDate: Optional(endDate));
  }

  void setSalesOrdersDateFilter(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(
        salesOrdersStartDate: Optional(startDate),
        salesOrdersEndDate: Optional(endDate));
  }

  void setSelectedBranches(List<RetailBranch> branches) {
    state = state.copyWith(selectedBranches: branches);
  }

  void deleteOrder(String transactionId) async {
    final transactions =
        state.transactions.value!.where((x) => x.id != transactionId).toList();
    state = state.copyWith(transactions: AsyncData(transactions));
  }

  void hideOrderDetailsError() {
    state = state.copyWith(showOrderDetailsError: false);
  }

  void hideShipmentError() {
    state = state.copyWith(showShipmentError: false);
  }

  void updateSearchedVariants(List<Variant> variants) {
    state = state.copyWith(searchedVariants: AsyncData(variants));
  }
}
